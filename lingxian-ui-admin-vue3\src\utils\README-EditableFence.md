# 可编辑围栏/区域系统 - 完整指南

## 概述

本系统提供了一套完整的可编辑围栏和区域管理解决方案，支持圆形、矩形两种类型，具备可编辑尺寸文本功能。系统将原本分散的围栏和区域功能整合到统一的fence.ts文件中，避免代码重复，便于维护。

## 核心特性

### ✅ 双击编辑
- 双击围栏上的尺寸数字进入编辑模式
- 支持圆形半径、矩形宽高的独立编辑
- 自动验证输入值，无效值自动恢复

### ✅ 实时更新
- 围栏尺寸根据输入值实时变化
- 提供更新回调机制，便于后端数据同步
- 支持坐标转换和缩放适配

### ✅ 多种类型
- **圆形围栏**：支持半径编辑，显示圆心标记和半径线
- **矩形围栏**：支持宽度和高度独立编辑
- **区域标注**：统一的区域尺寸标注管理

### ✅ 样式自定义
- 支持自定义颜色和样式
- 编辑状态视觉反馈
- 缩放级别适配

## 架构设计

```
fence.ts
├── FenceFactory (围栏工厂类)
│   ├── 围栏创建方法
│   ├── 区域尺寸文本方法
│   └── 事件处理方法
├── EditableFence (可编辑围栏类)
│   ├── 围栏创建和管理
│   ├── 尺寸文本编辑
│   └── 实时更新逻辑
├── AreaFenceManager (区域围栏管理器)
│   ├── 区域尺寸标注
│   ├── 围栏创建
│   └── 统一管理接口
└── EditableFenceManager (围栏管理器)
    ├── 多围栏管理
    ├── 全局回调
    └── 批量操作
```

## 类详细说明

### 1. FenceFactory (围栏工厂类)

基础围栏创建和操作类，从FabricCanvas中迁移而来。

```typescript
const fenceFactory = new FenceFactory(canvas)

// 创建围栏
const circle = fenceFactory.addCircleFence({ x: 100, y: 100 }, 50)
const rectangle = fenceFactory.addRectangleFence({ x: 200, y: 200 }, 100, 80)

// 区域尺寸文本
const radiusText = fenceFactory.addRadiusText({ x: 100, y: 100 }, 50)
const widthText = fenceFactory.addWidthText({ x: 200, y: 200 }, 100)
const heightText = fenceFactory.addHeightText({ x: 200, y: 200 }, 80)

// 圆心标记和半径线
const centerMark = fenceFactory.addCircleCenter({ x: 100, y: 100 })
const radiusLine = fenceFactory.addRadiusLine({ x: 100, y: 100 })
```

### 2. EditableFence (可编辑围栏类)

提供完整的可编辑围栏功能。

```typescript
const editableFence = new EditableFence(
  map,           // FabricCanvas实例
  imgInfoSize,   // 图片尺寸信息
  screenInfo,    // 屏幕信息
  'circle',      // 围栏类型
  'fence-1'      // 围栏ID
)

// 设置更新回调
editableFence.setUpdateCallback((fenceData) => {
  console.log('围栏数据更新:', fenceData)
  // 同步到后端
})

// 创建圆形围栏
editableFence.createCircleFence({ x: 100, y: 100 }, 50)

// 创建矩形围栏
editableFence.createRectangleFence({ x: 200, y: 200 }, 100, 80)

// 获取围栏数据
const fenceData = editableFence.getFenceData()

// 销毁围栏
editableFence.destroy()
```

### 3. AreaFenceManager (区域围栏管理器) 🆕

统一管理区域和围栏功能，整合了原2dMap.ts中的AreaDimensionManager。

```typescript
const areaFenceManager = new AreaFenceManager(canvas, imgInfoSize, screenInfo)

// 添加区域尺寸标注
const markers = areaFenceManager.addAreaDimensionMarkers(areaData, true)

// 更新区域尺寸标注
areaFenceManager.updateAreaDimensionMarkers(areaData, true, false)

// 切换可见性
areaFenceManager.toggleAreaDimensionVisibility(areaId, true)

// 移除标注
areaFenceManager.removeAreaDimensionMarkers(areaId)

// 创建围栏/区域对象
const fence = areaFenceManager.createFence(areaPosition)

// 获取围栏工厂
const factory = areaFenceManager.getFenceFactory()
```

### 4. EditableFenceManager (围栏管理器)

管理多个可编辑围栏实例。

```typescript
const manager = new EditableFenceManager(map, imgInfoSize, screenInfo)

// 设置全局更新回调
manager.setGlobalUpdateCallback((id, fenceData) => {
  console.log(`围栏 ${id} 更新:`, fenceData)
})

// 创建围栏
const fenceId = manager.createFence('circle', 'fence-1')
manager.createCircleFence(fenceId, { x: 100, y: 100 }, 50)

// 获取围栏
const fence = manager.getFence(fenceId)

// 销毁围栏
manager.destroyFence(fenceId)

// 销毁所有围栏
manager.destroyAll()
```

## 使用示例

### 基础使用

```typescript
import { EditableFence, AreaFenceManager } from '@/utils/fence'

// 1. 创建可编辑围栏
const fence = new EditableFence(map, imgInfoSize, screenInfo, 'circle')
fence.createCircleFence({ x: 100, y: 100 }, 50)

// 2. 使用区域管理器
const areaManager = new AreaFenceManager(canvas, imgInfoSize, screenInfo)
const markers = areaManager.addAreaDimensionMarkers(areaData, true)
```

### 高级使用

```typescript
// 使用组合式函数
const { 
  manager, 
  createFence, 
  updateFence, 
  destroyFence 
} = useEditableFence(map, imgInfoSize, screenInfo)

// 创建围栏并设置回调
const fenceId = createFence('rectangle', 'my-fence')
manager.setGlobalUpdateCallback((id, data) => {
  // 处理更新
})
```

## 事件系统

### 尺寸变化事件

```typescript
// 监听画布的dimension:changed事件
map.canvas.on('dimension:changed', (e) => {
  const { textObj, newValue, dimensionType } = e
  console.log(`${dimensionType} 更新为: ${newValue}`)
})
```

### 自定义回调

```typescript
fence.setUpdateCallback((fenceData) => {
  // 围栏数据变化时触发
  console.log('围栏更新:', fenceData)
  
  // 同步到后端
  updateFenceAPI(fenceData)
})
```

## 数据格式

### 围栏数据

```typescript
interface FenceDate {
  type: 'circle' | 'rectangle'
  points: {
    // 圆形
    x: number
    y: number
    radius: number
  } | {
    // 矩形
    x: number
    y: number
    width: number
    height: number
  }
}
```

### 区域位置数据

```typescript
interface AreaPosition {
  type: 'circle' | 'rectangle' | 'polygon' | 'none'
  points: CirclePoints | RectanglePoints | PolygonPoints
}
```

## 迁移指南

### 从FabricCanvas迁移

原FabricCanvas中的围栏相关方法已迁移到fence.ts：

```typescript
// 旧方式
map.addCircleFence(pos, radius)
map.addRectangleFence(pos, width, height)
map.addRadiusText(center, radius)

// 新方式
const factory = new FenceFactory(map.canvas)
factory.addCircleFence(pos, radius)
factory.addRectangleFence(pos, width, height)
factory.addRadiusText(center, radius)
```

### 从2dMap.ts迁移

原AreaDimensionManager已整合到AreaFenceManager：

```typescript
// 旧方式
import { AreaDimensionManager } from '@/views/master/area/components/map/2dMap'
AreaDimensionManager.addAreaDimensionMarkers(areaData, map, imgInfoSize, screenInfo, true)

// 新方式
import { AreaFenceManager } from '@/utils/fence'
const manager = new AreaFenceManager(canvas, imgInfoSize, screenInfo)
manager.addAreaDimensionMarkers(areaData, true)
```

## 最佳实践

### 1. 统一使用AreaFenceManager

推荐使用AreaFenceManager作为统一入口：

```typescript
// 推荐
const manager = new AreaFenceManager(canvas, imgInfoSize, screenInfo)
const fence = manager.createFence(areaPosition)
const markers = manager.addAreaDimensionMarkers(areaData, true)

// 而不是分别使用
const factory = new FenceFactory(canvas)
const editableFence = new EditableFence(...)
```

### 2. 合理使用回调

```typescript
// 设置全局回调处理所有围栏更新
manager.setGlobalUpdateCallback((id, data) => {
  // 统一处理逻辑
})

// 或为特定围栏设置回调
fence.setUpdateCallback((data) => {
  // 特定处理逻辑
})
```

### 3. 及时清理资源

```typescript
// 组件销毁时清理
onUnmounted(() => {
  manager.destroyAll()
  areaManager.removeAllAreaDimensionMarkers()
})
```

## 注意事项

1. **坐标系统**：确保imgInfoSize包含正确的比例尺信息
2. **缩放适配**：文本和线条会自动适配画布缩放级别
3. **事件处理**：双击编辑功能依赖mousedown事件
4. **数据验证**：输入值会自动验证，无效值恢复原值
5. **性能优化**：大量围栏时建议使用管理器批量操作

## 故障排除

### 常见问题

1. **文本不可编辑**：检查IText对象的editable属性
2. **尺寸不更新**：确保dimension:changed事件正确触发
3. **坐标转换错误**：检查imgInfoSize的比例尺配置
4. **缩放显示异常**：确保originalFontSize等属性正确设置

### 调试技巧

```typescript
// 启用调试日志
console.log('围栏数据:', fence.getFenceData())
console.log('画布对象:', map.canvas.getObjects())

// 检查事件绑定
textObj.on('editing:exited', () => {
  console.log('编辑完成')
})
```

这个统一的围栏/区域系统提供了完整的可编辑功能，将原本分散的代码整合到一起，提高了代码的可维护性和复用性。 